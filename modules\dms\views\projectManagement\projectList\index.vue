<template>
    <div class="project-management-list">
        <div class="content query-label-line2">
            <project-list
                v-model="activeNavTab"
                :nav-items="navItems"
                :query-params="queryParams"
                :query-config="queryConfig"
                :columns="columns"
                :data="productData"
                :load-data="loadProductData"
                :total="total"
                :page.sync="page"
                :limit.sync="limit"
                @search="handleSearch"
                @reset="handleReset"
                @nav-change="handleNavChange"
                @selection-change="handleSelectionChange"
                @pagination="handlePagination"
            >
                <template #rightNav>
                    <div class="right-nav-container">
                        <el-button type="text" @click="addTemporaryProject" class="create-demand-button"
                            ><i class="el-icon-plus"></i> 新增临时项目
                        </el-button>
                        <el-button type="text" @click="addFormalProject" class="create-demand-button"
                            ><i class="el-icon-plus"></i> 新增正式项目
                        </el-button>
                        <el-button type="text" @click="createDemand" class="create-demand-button"
                            ><i class="el-icon-download"></i> 下载项目信息
                        </el-button>
                    </div>
                </template>
                <!-- 操作列插槽 -->
                <template #actions="{ row }">
                    <el-button type="text" size="small" @click="handleEdit(row)">编辑</el-button>
                    <el-button type="text" size="small" @click="handleView(row)">查看</el-button>
                </template>
            </project-list>
        </div>

        <!-- 新增临时项目弹窗 -->
        <TemporaryProjectDialog
            :visible.sync="temporaryProjectDialogVisible"
            @success="handleTemporaryProjectSuccess"
        />
        <!-- 查看临时项目详情弹窗 -->
        <TemporaryProjectDetailDialog :visible.sync="temporaryProjectDetailDialogVisible" />

        <!-- 新增正式项目弹窗 -->
        <FormalProjectDialog :visible.sync="formalProjectDialogVisible" @success="handleFormalProjectSuccess" />
        <!-- 查看正式项目详情弹窗 -->
        <FormalProjectDetailDialog :visible.sync="formalProjectDetailDialogVisible" />
    </div>
</template>

<script>
import ProjectList from 'dms/views/projectManagement/components/projectList/index.vue';
import TemporaryProjectDialog from './components/TemporaryProjectDialog.vue';
import TemporaryProjectDetailDialog from './components/TemporaryProjectDetailDialog.vue';
import FormalProjectDialog from './components/FormalProjectDialog.vue';
import FormalProjectDetailDialog from './components/FormalProjectDetailDialog.vue';
import { queryConfig, queryParams, navItems, productColumns } from './config.js';

export default {
    name: 'ProjectManagementList',
    components: {
        ProjectList,
        TemporaryProjectDialog,
        TemporaryProjectDetailDialog,
        FormalProjectDialog,
        FormalProjectDetailDialog
    },
    data() {
        return {
            // 当前激活的导航标签
            activeNavTab: 'all',
            // 导航栏配置
            navItems,
            // 查询参数
            queryParams: this.$tools.cloneDeep(queryParams),
            // 查询表单配置
            queryConfig,
            // 表格列配置
            columns: productColumns,
            // 产品数据
            productData: [{}],
            // 分页配置
            total: 0,
            page: 1,
            limit: 10,
            // 新增/编辑临时项目弹窗
            temporaryProjectDialogVisible: false,
            // 查看临时项目详情弹窗
            temporaryProjectDetailDialogVisible: false,
            // 新增/编辑正式项目弹窗
            formalProjectDialogVisible: false,
            // 查看正式项目详情弹窗
            formalProjectDetailDialogVisible: false
        };
    },
    mounted() {
        this.loadProductData();
    },
    methods: {
        // 加载产品数据
        async loadProductData() {
            try {
                const params = {
                    page: this.page,
                    limit: this.limit,
                    ...this.queryParams
                };

                // 日期处理
                if (params.foundDate && params.foundDate.length > 0) {
                    params.startDateString = params.foundDate[0];
                    params.endDateString = params.foundDate[1];
                }
                if (params.closeDate && params.closeDate.length > 0) {
                    params.closeStartDateString = params.closeDate[0];
                    params.closeEndDateString = params.closeDate[1];
                }

                const response = await this.$service.dms.common.getProjectOrGroupList(params);

                if (response.code === '000000') {
                    this.productData = response.result?.list || [];
                    this.total = response.result?.total || 0;
                } else {
                    this.$message.error(response.message || '获取产品列表失败');
                }
            } catch (error) {
                console.error('加载产品数据失败:', error);
                this.$message.error('加载产品数据失败');
            }
        },

        // 处理搜索
        handleSearch() {
            this.page = 1;
            this.loadProductData();
        },

        // 处理重置
        handleReset() {
            this.page = 1;
            this.loadProductData();
        },

        // 处理导航切换
        handleNavChange() {
            this.page = 1;
            this.loadProductData();
        },

        // 处理选择变化
        handleSelectionChange(selection) {
            console.log('选中的产品:', selection);
        },

        // 处理分页变化
        handlePagination() {
            this.loadProductData();
        },

        // 编辑产品
        handleEdit(row) {},

        // 查看产品详情
        handleView(row) {
            // TODO: 根据项目类型判断
            // this.formalProjectDetailDialogVisible = true;
            this.temporaryProjectDetailDialogVisible = true;
        },

        /**
         * 新增临时项目
         */
        addTemporaryProject() {
            this.temporaryProjectDialogVisible = true;
        },

        /**
         * 新增正式项目
         */
        addFormalProject() {
            this.formalProjectDialogVisible = true;
        },

        /**
         * 创建需求
         */
        createDemand() {
            // TODO: 实现创建需求功能
            this.$message.info('创建需求功能待开发');
        },

        /**
         * 临时项目创建成功回调
         */
        handleTemporaryProjectSuccess() {
            this.$message.success('临时项目创建成功');
            // 重新加载数据
            this.loadProductData();
        },

        /**
         * 新增
         */
        handleAdd() {
            this.$router.push({
                name: 'AddProduct'
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.project-management-list {
    padding: 20px;
}
.right-nav-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-right: 10px;
}
</style>
