import CommonItems from 'snbcCommon/common/form-items.js';

const { select, input, monthRange } = CommonItems;

// 部门
const department = {
    ...select,
    name: '部门',
    modelKey: 'department'
};

// 产品线
const productLine = {
    ...select,
    name: '产品线',
    modelKey: 'productLine'
};

// 项目名称
const projectName = {
    ...input,
    name: '项目名称',
    modelKey: 'projectName'
};

// 项目经理
const projectManager = {
    ...input,
    name: '项目经理',
    modelKey: 'projectManager'
};

// 产品经理
const productManager = {
    ...input,
    name: '产品经理',
    modelKey: 'productManager'
};

// 项目ID
const projectId = {
    ...input,
    name: '项目ID',
    modelKey: 'projectId'
};

// 涉及产品
const involvedProducts = {
    ...input,
    name: '涉及产品',
    modelKey: 'involvedProducts'
};

// 进度状态
const progressStatus = {
    ...select,
    name: '进度状态',
    modelKey: 'progressStatus'
};

// 开始时间
const startTime = {
    ...monthRange,
    name: '开始时间',
    modelKey: 'startTime',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM'
    }
};

// 计划结束时间
const plannedEndTime = {
    ...monthRange,
    name: '计划结束时间',
    modelKey: 'plannedEndTime',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM'
    }
};

// 实际结束时间
const actualEndTime = {
    ...monthRange,
    name: '实际结束时间',
    modelKey: 'actualEndTime',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM'
    }
};

// 公司立项项目
const companyProjectTime = {
    ...input,
    name: '公司立项项目',
    modelKey: 'companyProjectTime'
};

// 查询区域配置项
export const queryConfig = {
    elFormAttrs: {
        'size': 'small',
        'inline': true,
        'label-width': '120px'
    },
    items: [
        department,
        productLine,
        projectName,
        projectManager,
        productManager,
        projectId,
        involvedProducts,
        progressStatus,
        startTime,
        plannedEndTime,
        actualEndTime,
        companyProjectTime
    ]
};

// 查询条件参数
export const queryParams = {
    department: '',
    productLine: '',
    projectName: '',
    projectManager: '',
    productManager: '',
    projectId: '',
    involvedProducts: '',
    progressStatus: '',
    startTime: [],
    plannedEndTime: [],
    actualEndTime: [],
    companyProjectTime: ''
};

// 导航栏配置
export const navItems = [
    { field: '', name: '所有', queryField: 'progressStatus' },
    { field: 'active', name: '待启动', queryField: 'progressStatus' },
    { field: 'active', name: '进行中', queryField: 'progressStatus' },
    { field: 'paused', name: '暂停', queryField: 'progressStatus' },
    { field: 'closed', name: '结项', queryField: 'progressStatus' },
    { field: 'closed', name: '终止', queryField: 'progressStatus' }
];

// 产品列表表格列配置
export const productColumns = [
    {
        type: 'selection',
        width: 55,
        label: '选择框',
        columnManage: {
            sortableDisabled: true,
            pinnedFirst: true,
            widthDisabled: true
        }
    },
    {
        prop: 'index',
        label: '序号',
        width: 80,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productId',
        label: 'ID',
        width: 160,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productLine',
        label: '部门',
        width: 160,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productNumber',
        label: '产品线',
        width: 160,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productName',
        label: '产品编号',
        width: 200,
        showOverflowTooltip: false,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productOwner',
        label: '项目名称',
        width: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'foundDate',
        label: '项目经理',
        width: 160,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    },
    {
        prop: 'productStatus',
        label: '涉及产品',
        width: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'showMoney',
        label: '项目状态',
        width: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'closeDate',
        label: '进度状态',
        width: 160,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    },
    {
        prop: 'startTime',
        label: '项目开始时间',
        width: 160,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    },
    {
        prop: 'plannedEndTime',
        label: '计划结束时间',
        width: 160,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    },
    {
        prop: 'actualEndTime',
        label: '实际结束时间',
        width: 160,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    },
    {
        prop: 'companyProject',
        label: '公司立项项目',
        width: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    }
];
